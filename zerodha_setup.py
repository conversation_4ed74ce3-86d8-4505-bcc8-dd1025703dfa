#!/usr/bin/env python3
"""
Zerodha API Setup Helper
========================
Helps you get your API credentials and test connection.

Usage: python3 zerodha_setup.py
"""

import sys
import webbrowser
from urllib.parse import urlparse, parse_qs

try:
    from kiteconnect import KiteConnect
    KITE_AVAILABLE = True
except ImportError:
    KITE_AVAILABLE = False
    print("❌ Install kiteconnect first: pip install kiteconnect")
    sys.exit(1)

def setup_api_credentials():
    """Guide user through API setup"""
    print("🔗 ZERODHA API SETUP HELPER")
    print("="*40)
    print("This will help you get your API credentials")
    print()
    
    # Step 1: Get API Key
    print("📋 STEP 1: Get API Key")
    print("-" * 25)
    print("1. Opening Kite Connect Developer Console...")
    
    try:
        webbrowser.open("https://developers.kite.trade/")
        print("✅ Browser opened")
    except:
        print("🌐 Please visit: https://developers.kite.trade/")
    
    print()
    print("2. Login with your Zerodha credentials")
    print("3. Click 'Create new app'")
    print("4. Fill in:")
    print("   - App name: My Trading Bot")
    print("   - App type: Connect")
    print("   - Redirect URL: http://localhost:8080")
    print("5. Save the app")
    print()
    
    api_key = input("Enter your API Key: ").strip()
    api_secret = input("Enter your API Secret: ").strip()
    
    if not api_key or not api_secret:
        print("❌ Both API Key and Secret are required")
        return None, None, None
    
    # Step 2: Get Request Token
    print("\n📋 STEP 2: Get Request Token")
    print("-" * 30)
    
    login_url = f"https://kite.trade/connect/login?api_key={api_key}"
    print(f"1. Opening login URL...")
    
    try:
        webbrowser.open(login_url)
        print("✅ Browser opened")
    except:
        print(f"🌐 Please visit: {login_url}")
    
    print()
    print("2. Login with your Zerodha credentials")
    print("3. Authorize the app")
    print("4. Copy the 'request_token' from the redirect URL")
    print("   (URL will look like: http://localhost:8080/?request_token=XXXXX&action=login&status=success)")
    print()
    
    redirect_url = input("Paste the full redirect URL here: ").strip()
    
    # Extract request token
    try:
        parsed_url = urlparse(redirect_url)
        query_params = parse_qs(parsed_url.query)
        request_token = query_params.get('request_token', [None])[0]
        
        if not request_token:
            print("❌ Could not extract request_token from URL")
            request_token = input("Enter request_token manually: ").strip()
    except:
        request_token = input("Enter request_token manually: ").strip()
    
    if not request_token:
        print("❌ Request token is required")
        return None, None, None
    
    return api_key, api_secret, request_token

def generate_access_token(api_key, api_secret, request_token):
    """Generate access token"""
    print("\n📋 STEP 3: Generate Access Token")
    print("-" * 35)
    
    try:
        kite = KiteConnect(api_key=api_key)
        data = kite.generate_session(request_token, api_secret=api_secret)
        access_token = data["access_token"]
        
        print(f"✅ Access Token Generated!")
        print(f"🔑 Access Token: {access_token}")
        print()
        print("💾 SAVE THIS TOKEN - You'll need it for trading!")
        print("⏰ Token is valid until you logout from Kite")
        
        return access_token
        
    except Exception as e:
        print(f"❌ Failed to generate access token: {e}")
        return None

def test_connection(api_key, access_token):
    """Test API connection"""
    print("\n📋 STEP 4: Test Connection")
    print("-" * 27)
    
    try:
        kite = KiteConnect(api_key=api_key)
        kite.set_access_token(access_token)
        
        # Test profile
        profile = kite.profile()
        print(f"✅ Connected successfully!")
        print(f"👤 User: {profile.get('user_name', 'N/A')}")
        print(f"📧 Email: {profile.get('email', 'N/A')}")
        
        # Test margins
        margins = kite.margins()
        cash = margins['equity']['available']['cash']
        print(f"💰 Available Cash: ₹{cash:,}")
        
        if cash >= 2000:
            print("✅ Sufficient funds for trading")
        else:
            print("⚠️ Consider adding more funds for trading")
        
        # Test market data
        nifty_ltp = kite.ltp(["NSE:NIFTY 50"])
        nifty_price = nifty_ltp["NSE:NIFTY 50"]["last_price"]
        print(f"📊 NIFTY 50: ₹{nifty_price:,}")
        
        print("\n🎉 API Setup Complete!")
        print("✅ You can now run: python3 zerodha_trader.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def save_credentials(api_key, access_token):
    """Save credentials to file"""
    print("\n💾 Save Credentials?")
    print("-" * 20)
    print("⚠️ This will save your credentials to 'zerodha_config.py'")
    print("🔒 Keep this file secure and don't share it")
    
    save = input("Save credentials? (y/n): ").lower().strip()
    
    if save == 'y':
        config_content = f'''# Zerodha API Configuration
# Keep this file secure!

API_KEY = "{api_key}"
ACCESS_TOKEN = "{access_token}"
'''
        
        try:
            with open('zerodha_config.py', 'w') as f:
                f.write(config_content)
            print("✅ Credentials saved to 'zerodha_config.py'")
            print("🔒 Keep this file secure!")
        except Exception as e:
            print(f"❌ Failed to save: {e}")

def main():
    """Main setup process"""
    print("🚀 Welcome to Zerodha API Setup!")
    print("This will guide you through getting your API credentials.")
    print()
    
    # Get credentials
    api_key, api_secret, request_token = setup_api_credentials()
    
    if not all([api_key, api_secret, request_token]):
        print("❌ Setup incomplete")
        return
    
    # Generate access token
    access_token = generate_access_token(api_key, api_secret, request_token)
    
    if not access_token:
        print("❌ Could not generate access token")
        return
    
    # Test connection
    if test_connection(api_key, access_token):
        save_credentials(api_key, access_token)
        print("\n🎉 Setup Complete!")
        print("Next step: python3 zerodha_trader.py")
    else:
        print("❌ Setup failed")

if __name__ == "__main__":
    main()
