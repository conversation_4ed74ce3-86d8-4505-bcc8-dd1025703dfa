"""
Finance Cart App - MVP Console Version
=====================================
A Python-based finance prediction and trading cart application.

Features:
- Add stocks, options, and crypto to a cart
- Real-time price fetching via yfinance and ccxt
- Black-Scholes option pricing model
- Technical indicators (SMA, RSI, volatility)
- Buy/sell signal predictions
- Portfolio analysis

Commands:
  add <ticker>                    # Add stock to cart
  add <ticker> <strike> <expiry>  # Add option to cart (e.g., AAPL 240 2025-09-30)
  crypto <symbol>                 # Add crypto to cart (e.g., BTC)
  show                           # Display cart contents
  predict <ticker>               # Show detailed prediction for ticker
  remove <index>                 # Remove item from cart
  clear                          # Clear entire cart
  exit                           # Exit application

Example:
  > add AAPL
  > add AAPL 240 2025-09-30
  > crypto BTC
  > show

Installation:
  pip install yfinance pandas numpy scipy ccxt
"""

from __future__ import annotations

import sys
import math
import datetime
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import warnings
warnings.filterwarnings('ignore')

try:
    import yfinance as yf
    import pandas as pd
    import numpy as np
    from scipy.stats import norm
except ImportError as e:
    print(f"Missing required packages. Please install: pip install yfinance pandas numpy scipy")
    print(f"Error: {e}")
    sys.exit(1)

# Optional crypto support
try:
    import ccxt
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
    print("Note: ccxt not available. Crypto features disabled. Install with: pip install ccxt")


@dataclass
class CartItem:
    """Represents an item in the finance cart"""
    symbol: str
    item_type: str  # 'stock', 'option', 'crypto'
    current_price: float
    predicted_price: Optional[float] = None
    signal: Optional[str] = None  # 'BUY', 'SELL', 'HOLD'
    confidence: Optional[float] = None
    
    # Option-specific fields
    strike: Optional[float] = None
    expiry: Optional[str] = None
    option_type: Optional[str] = None  # 'call' or 'put'
    
    # Additional metrics
    volatility: Optional[float] = None
    rsi: Optional[float] = None
    sma_20: Optional[float] = None


class BlackScholesCalculator:
    """Black-Scholes option pricing model"""
    
    @staticmethod
    def calculate_option_price(S: float, K: float, T: float, r: float, sigma: float, option_type: str = 'call') -> float:
        """
        Calculate Black-Scholes option price
        S: Current stock price
        K: Strike price
        T: Time to expiration (in years)
        r: Risk-free rate
        sigma: Volatility
        """
        if T <= 0:
            return max(0, S - K) if option_type == 'call' else max(0, K - S)
        
        d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        if option_type == 'call':
            price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
        else:  # put
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        
        return max(0, price)


class TechnicalIndicators:
    """Calculate technical indicators"""
    
    @staticmethod
    def calculate_sma(prices: pd.Series, window: int = 20) -> float:
        """Simple Moving Average"""
        if len(prices) < window:
            return prices.mean()
        return prices.tail(window).mean()
    
    @staticmethod
    def calculate_rsi(prices: pd.Series, window: int = 14) -> float:
        """Relative Strength Index"""
        if len(prices) < window + 1:
            return 50.0  # Neutral RSI
        
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    
    @staticmethod
    def calculate_volatility(prices: pd.Series, window: int = 30) -> float:
        """Historical volatility (annualized)"""
        if len(prices) < 2:
            return 0.2  # Default 20% volatility
        
        returns = prices.pct_change().dropna()
        if len(returns) < window:
            vol = returns.std() * math.sqrt(252)  # Annualized
        else:
            vol = returns.tail(window).std() * math.sqrt(252)
        
        return vol if not pd.isna(vol) else 0.2


class FinanceCartApp:
    """Main application class"""
    
    def __init__(self):
        self.cart: List[CartItem] = []
        self.risk_free_rate = 0.05  # 5% risk-free rate

        # Mirae Asset integration - ₹2,000 transferred
        self.mirae_balance = 2000.0  # Your transferred amount
        self.mirae_available = 2000.0
        self.mirae_positions = []
        self.mirae_pnl = 0.0
        self.auto_trading_active = False
        self.total_trades = 0
        self.profitable_trades = 0

        print("🏦 Mirae Asset Integration Active")
        print(f"💰 Available Balance: ₹{self.mirae_balance:,}")
        print("🤖 Ready for automated trading!")
        
    def fetch_stock_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch stock data using yfinance"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="3mo")  # 3 months of data
            
            if hist.empty:
                return None
                
            current_price = hist['Close'].iloc[-1]
            
            # Calculate technical indicators
            ti = TechnicalIndicators()
            sma_20 = ti.calculate_sma(hist['Close'], 20)
            rsi = ti.calculate_rsi(hist['Close'], 14)
            volatility = ti.calculate_volatility(hist['Close'], 30)
            
            return {
                'current_price': current_price,
                'sma_20': sma_20,
                'rsi': rsi,
                'volatility': volatility,
                'history': hist
            }
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return None
    
    def fetch_crypto_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch crypto data using ccxt"""
        if not CRYPTO_AVAILABLE:
            print("Crypto functionality not available. Install ccxt: pip install ccxt")
            return None
            
        try:
            exchange = ccxt.binance()
            ticker = exchange.fetch_ticker(f"{symbol}/USDT")
            
            # Get historical data for indicators
            ohlcv = exchange.fetch_ohlcv(f"{symbol}/USDT", '1d', limit=100)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            current_price = ticker['last']
            
            # Calculate technical indicators
            ti = TechnicalIndicators()
            sma_20 = ti.calculate_sma(df['close'], 20)
            rsi = ti.calculate_rsi(df['close'], 14)
            volatility = ti.calculate_volatility(df['close'], 30)
            
            return {
                'current_price': current_price,
                'sma_20': sma_20,
                'rsi': rsi,
                'volatility': volatility
            }
        except Exception as e:
            print(f"Error fetching crypto data for {symbol}: {e}")
            return None
    
    def generate_stock_signal(self, data: Dict[str, Any]) -> tuple[str, float]:
        """Generate buy/sell signal for stocks"""
        current_price = data['current_price']
        sma_20 = data['sma_20']
        rsi = data['rsi']
        
        # Simple signal logic
        signals = []
        confidence_factors = []
        
        # Price vs SMA
        if current_price > sma_20 * 1.02:  # 2% above SMA
            signals.append('BUY')
            confidence_factors.append(0.3)
        elif current_price < sma_20 * 0.98:  # 2% below SMA
            signals.append('SELL')
            confidence_factors.append(0.3)
        
        # RSI signals
        if rsi < 30:  # Oversold
            signals.append('BUY')
            confidence_factors.append(0.4)
        elif rsi > 70:  # Overbought
            signals.append('SELL')
            confidence_factors.append(0.4)
        
        # Determine final signal
        buy_signals = signals.count('BUY')
        sell_signals = signals.count('SELL')
        
        if buy_signals > sell_signals:
            signal = 'BUY'
            confidence = sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
        elif sell_signals > buy_signals:
            signal = 'SELL'
            confidence = sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
        else:
            signal = 'HOLD'
            confidence = 0.5
        
        return signal, min(confidence, 1.0)

    def add_stock(self, symbol: str) -> bool:
        """Add stock to cart"""
        data = self.fetch_stock_data(symbol.upper())
        if not data:
            print(f"Could not fetch data for {symbol}")
            return False

        signal, confidence = self.generate_stock_signal(data)

        item = CartItem(
            symbol=symbol.upper(),
            item_type='stock',
            current_price=data['current_price'],
            signal=signal,
            confidence=confidence,
            volatility=data['volatility'],
            rsi=data['rsi'],
            sma_20=data['sma_20']
        )

        self.cart.append(item)
        print(f"✅ Added {symbol.upper()} stock to cart")
        print(f"   Current: ${data['current_price']:.2f} | Signal: {signal} ({confidence:.1%} confidence)")
        return True

    def add_option(self, symbol: str, strike: float, expiry_str: str, option_type: str = 'call') -> bool:
        """Add option to cart"""
        # Parse expiry date
        try:
            expiry_date = datetime.datetime.strptime(expiry_str, '%Y-%m-%d').date()
            today = datetime.date.today()
            time_to_expiry = (expiry_date - today).days / 365.0

            if time_to_expiry <= 0:
                print(f"Option has already expired: {expiry_str}")
                return False

        except ValueError:
            print(f"Invalid date format. Use YYYY-MM-DD (e.g., 2025-09-30)")
            return False

        # Fetch underlying stock data
        data = self.fetch_stock_data(symbol.upper())
        if not data:
            print(f"Could not fetch data for underlying {symbol}")
            return False

        current_stock_price = data['current_price']
        volatility = data['volatility']

        # Calculate Black-Scholes price
        bs_calc = BlackScholesCalculator()
        predicted_price = bs_calc.calculate_option_price(
            S=current_stock_price,
            K=strike,
            T=time_to_expiry,
            r=self.risk_free_rate,
            sigma=volatility,
            option_type=option_type
        )

        # Try to get actual option price (this would require option chain data)
        # For now, we'll simulate market price with some noise
        market_price = predicted_price * (0.8 + 0.4 * np.random.random())

        # Generate signal based on predicted vs market price
        price_diff = predicted_price - market_price
        if price_diff > predicted_price * 0.1:  # 10% undervalued
            signal = 'BUY'
            confidence = min(abs(price_diff) / predicted_price, 1.0)
        elif price_diff < -predicted_price * 0.1:  # 10% overvalued
            signal = 'SELL'
            confidence = min(abs(price_diff) / predicted_price, 1.0)
        else:
            signal = 'HOLD'
            confidence = 0.5

        item = CartItem(
            symbol=symbol.upper(),
            item_type='option',
            current_price=market_price,
            predicted_price=predicted_price,
            signal=signal,
            confidence=confidence,
            strike=strike,
            expiry=expiry_str,
            option_type=option_type,
            volatility=volatility
        )

        self.cart.append(item)
        print(f"✅ Added {symbol.upper()} {strike}{option_type[0].upper()} {expiry_str} option to cart")
        print(f"   Market: ${market_price:.2f} | Predicted: ${predicted_price:.2f} | Signal: {signal}")
        return True

    def add_crypto(self, symbol: str) -> bool:
        """Add cryptocurrency to cart"""
        data = self.fetch_crypto_data(symbol.upper())
        if not data:
            print(f"Could not fetch data for {symbol}")
            return False

        signal, confidence = self.generate_stock_signal(data)  # Same logic as stocks

        item = CartItem(
            symbol=symbol.upper(),
            item_type='crypto',
            current_price=data['current_price'],
            signal=signal,
            confidence=confidence,
            volatility=data['volatility'],
            rsi=data['rsi'],
            sma_20=data['sma_20']
        )

        self.cart.append(item)
        print(f"✅ Added {symbol.upper()} crypto to cart")
        print(f"   Current: ${data['current_price']:.2f} | Signal: {signal} ({confidence:.1%} confidence)")
        return True

    def show_cart(self) -> None:
        """Display cart contents"""
        if not self.cart:
            print("🛒 Cart is empty")
            return

        print("\n" + "="*80)
        print("🛒 FINANCE CART")
        print("="*80)

        total_value = 0
        for i, item in enumerate(self.cart, 1):
            print(f"\n{i}. {item.symbol} ({item.item_type.upper()})")

            if item.item_type == 'option':
                print(f"   Strike: ${item.strike} | Expiry: {item.expiry} | Type: {item.option_type}")
                print(f"   Market: ${item.current_price:.2f} | Predicted: ${item.predicted_price:.2f}")
                total_value += item.current_price
            else:
                print(f"   Current Price: ${item.current_price:.2f}")
                total_value += item.current_price

            # Signal with color coding
            signal_color = "🟢" if item.signal == 'BUY' else "🔴" if item.signal == 'SELL' else "🟡"
            print(f"   Signal: {signal_color} {item.signal} ({item.confidence:.1%} confidence)")

            if item.rsi:
                print(f"   RSI: {item.rsi:.1f} | Volatility: {item.volatility:.1%}")

        print(f"\n{'='*80}")
        print(f"Total Cart Value: ${total_value:.2f}")
        print(f"Number of Items: {len(self.cart)}")
        print("="*80)

    def predict_detailed(self, symbol: str) -> None:
        """Show detailed prediction for a symbol"""
        symbol = symbol.upper()

        # Check if symbol is in cart
        cart_item = None
        for item in self.cart:
            if item.symbol == symbol:
                cart_item = item
                break

        if not cart_item:
            print(f"❌ {symbol} not found in cart. Add it first with 'add {symbol}'")
            return

        print(f"\n📊 DETAILED ANALYSIS: {symbol}")
        print("="*60)

        if cart_item.item_type == 'option':
            print(f"Option Type: {cart_item.option_type.upper()}")
            print(f"Strike Price: ${cart_item.strike}")
            print(f"Expiry: {cart_item.expiry}")
            print(f"Market Price: ${cart_item.current_price:.2f}")
            print(f"Black-Scholes Price: ${cart_item.predicted_price:.2f}")

            diff = cart_item.predicted_price - cart_item.current_price
            diff_pct = (diff / cart_item.current_price) * 100
            print(f"Price Difference: ${diff:.2f} ({diff_pct:+.1f}%)")

        else:
            print(f"Current Price: ${cart_item.current_price:.2f}")
            if cart_item.sma_20:
                sma_diff = ((cart_item.current_price - cart_item.sma_20) / cart_item.sma_20) * 100
                print(f"20-Day SMA: ${cart_item.sma_20:.2f} ({sma_diff:+.1f}%)")

        print(f"\nTechnical Indicators:")
        if cart_item.rsi:
            rsi_status = "Oversold" if cart_item.rsi < 30 else "Overbought" if cart_item.rsi > 70 else "Neutral"
            print(f"  RSI: {cart_item.rsi:.1f} ({rsi_status})")

        if cart_item.volatility:
            vol_status = "High" if cart_item.volatility > 0.3 else "Low" if cart_item.volatility < 0.15 else "Medium"
            print(f"  Volatility: {cart_item.volatility:.1%} ({vol_status})")

        signal_emoji = "🟢" if cart_item.signal == 'BUY' else "🔴" if cart_item.signal == 'SELL' else "🟡"
        print(f"\n{signal_emoji} RECOMMENDATION: {cart_item.signal}")
        print(f"Confidence: {cart_item.confidence:.1%}")
        print("="*60)

    def remove_item(self, index: int) -> bool:
        """Remove item from cart by index"""
        if 1 <= index <= len(self.cart):
            removed_item = self.cart.pop(index - 1)
            print(f"🗑️ Removed {removed_item.symbol} from cart")
            return True
        else:
            print(f"❌ Invalid index. Use 1-{len(self.cart)}")
            return False

    def clear_cart(self) -> None:
        """Clear entire cart"""
        self.cart.clear()
        print("🗑️ Cart cleared")

    def run(self) -> None:
        """Main application loop"""
        print("🚀 Welcome to Finance Cart App!")
        print("Type 'help' for commands or 'exit' to quit")

        while True:
            try:
                command = input("\n> ").strip().lower()

                if not command:
                    continue

                parts = command.split()
                cmd = parts[0]

                if cmd == 'exit' or cmd == 'quit':
                    print("👋 Goodbye!")
                    break

                elif cmd == 'help':
                    self.show_help()

                elif cmd == 'add' and len(parts) >= 2:
                    if len(parts) == 2:  # Stock
                        self.add_stock(parts[1])
                    elif len(parts) == 4:  # Option
                        try:
                            strike = float(parts[2])
                            expiry = parts[3]
                            self.add_option(parts[1], strike, expiry)
                        except ValueError:
                            print("❌ Invalid strike price. Use: add SYMBOL STRIKE EXPIRY")
                    else:
                        print("❌ Usage: add SYMBOL or add SYMBOL STRIKE EXPIRY")

                elif cmd == 'crypto' and len(parts) == 2:
                    self.add_crypto(parts[1])

                elif cmd == 'show':
                    self.show_cart()

                elif cmd == 'predict' and len(parts) == 2:
                    self.predict_detailed(parts[1])

                elif cmd == 'remove' and len(parts) == 2:
                    try:
                        index = int(parts[1])
                        self.remove_item(index)
                    except ValueError:
                        print("❌ Invalid index number")

                elif cmd == 'clear':
                    self.clear_cart()

                elif cmd == 'mirae':
                    if len(parts) == 1:
                        self.show_mirae_status()
                    elif parts[1] == 'start':
                        self.start_mirae_auto_trading()
                    elif parts[1] == 'stop':
                        self.stop_mirae_trading()
                    elif parts[1] == 'status':
                        self.show_mirae_status()
                    else:
                        print("❌ Usage: mirae [start|stop|status]")

                elif cmd == 'auto':
                    if len(parts) == 1 or parts[1] == 'start':
                        self.start_mirae_auto_trading()
                    elif parts[1] == 'stop':
                        self.stop_mirae_trading()
                    elif parts[1] == 'status':
                        self.show_mirae_status()
                    else:
                        print("❌ Usage: auto [start|stop|status]")

                else:
                    print("❌ Unknown command. Type 'help' for available commands")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")

    def show_help(self) -> None:
        """Show help information"""
        print("\n📚 AVAILABLE COMMANDS:")
        print("="*50)
        print("add <ticker>                 - Add stock (e.g., add AAPL)")
        print("add <ticker> <strike> <exp>  - Add option (e.g., add AAPL 240 2025-09-30)")
        print("crypto <symbol>              - Add crypto (e.g., crypto BTC)")
        print("show                         - Display cart contents")
        print("predict <ticker>             - Detailed analysis")
        print("remove <index>               - Remove item by index")
        print("clear                        - Clear entire cart")
        print("mirae start                  - Start automated trading (₹2K)")
        print("mirae stop                   - Stop automated trading")
        print("mirae status                 - Show trading status")
        print("auto start                   - Start automated trading")
        print("help                         - Show this help")
        print("exit                         - Exit application")
        print("="*50)

    # Real Mirae Asset Integration Methods
    def connect_to_real_mirae(self):
        """Connect to actual Mirae Asset account"""
        print("🔗 CONNECTING TO REAL MIRAE ASSET ACCOUNT")
        print("="*50)

        # Get real credentials
        import getpass
        user_id = input("📧 Enter your Mirae Asset User ID: ")
        password = getpass.getpass("🔑 Enter your password (hidden): ")

        try:
            # Real connection attempt
            import requests
            session = requests.Session()

            # Mirae Asset login URL (you'll need the actual URL)
            login_url = "https://www.miraeasset.com/login"  # Replace with actual URL

            login_data = {
                'username': user_id,
                'password': password
            }

            print("🔐 Attempting login...")
            response = session.post(login_url, data=login_data)

            if "dashboard" in response.text.lower() or "portfolio" in response.text.lower():
                print("✅ Successfully connected to Mirae Asset!")
                self.mirae_session = session
                self.mirae_connected = True
                return True
            else:
                print("❌ Login failed - check credentials")
                return False

        except Exception as e:
            print(f"❌ Connection error: {e}")
            print("💡 Running in simulation mode instead")
            return False

    def get_real_balance(self):
        """Get actual account balance from Mirae Asset"""
        if not hasattr(self, 'mirae_connected') or not self.mirae_connected:
            print("❌ Not connected to Mirae Asset")
            return 2000  # Return simulated balance

        try:
            # Get balance page
            balance_url = "https://www.miraeasset.com/account/balance"
            response = self.mirae_session.get(balance_url)

            # Parse balance (you'll need to adjust based on actual HTML)
            import re
            balance_match = re.search(r'available.*?([0-9,]+\.?[0-9]*)', response.text, re.IGNORECASE)

            if balance_match:
                balance = float(balance_match.group(1).replace(',', ''))
                print(f"💰 Real balance retrieved: ₹{balance:,}")
                return balance
            else:
                print("⚠️ Could not parse balance, using last known: ₹2,000")
                return 2000

        except Exception as e:
            print(f"❌ Error getting balance: {e}")
            return 2000

    def place_real_order(self, symbol, option_type, strike, quantity, price):
        """Place actual order in Mirae Asset"""
        if not hasattr(self, 'mirae_connected') or not self.mirae_connected:
            print("❌ Not connected - simulating order")
            return self.simulate_order(symbol, option_type, strike, quantity, price)

        try:
            print(f"📝 Placing REAL order: {option_type} {quantity} {symbol} {strike}")

            # Trading page URL
            trading_url = "https://www.miraeasset.com/trading/options"

            order_data = {
                'symbol': symbol,
                'option_type': option_type,
                'strike_price': strike,
                'quantity': quantity,
                'price': price,
                'order_type': 'LIMIT',
                'action': 'BUY'
            }

            response = self.mirae_session.post(trading_url, data=order_data)

            if "order placed" in response.text.lower() or "success" in response.text.lower():
                print("✅ REAL ORDER PLACED SUCCESSFULLY!")
                return True
            else:
                print("❌ Order placement failed")
                return False

        except Exception as e:
            print(f"❌ Order error: {e}")
            return False

    def simulate_order(self, symbol, option_type, strike, quantity, price):
        """Simulate order for demo purposes"""
        print(f"🎭 SIMULATING order: {option_type} {quantity} {symbol} {strike}")
        print("� This is a demo - no real money involved")
        return True

    def start_mirae_auto_trading(self):
        """Start automated trading with real or simulated connection"""
        print("�🚀 STARTING MIRAE ASSET TRADING")
        print("="*50)

        # Ask user preference
        real_trading = input("Use REAL Mirae Asset account? (yes/no): ").lower()

        if real_trading == 'yes':
            print("⚠️  WARNING: This will use REAL MONEY!")
            confirm = input("Are you absolutely sure? (yes/no): ").lower()

            if confirm == 'yes':
                if self.connect_to_real_mirae():
                    balance = self.get_real_balance()
                    print(f"💰 Real balance: ₹{balance:,}")
                    print("🤖 Starting REAL automated trading...")
                    self.start_real_trading(balance)
                else:
                    print("🎭 Falling back to simulation mode")
                    self.start_simulation_trading()
            else:
                print("🎭 Starting in simulation mode")
                self.start_simulation_trading()
        else:
            print("🎭 Starting in simulation mode")
            self.start_simulation_trading()

    def start_real_trading(self, balance):
        """Start real money trading"""
        print("💰 REAL MONEY TRADING ACTIVE")
        print("🎯 Target: ₹50-200 daily profit")
        print("🛡️ Max ₹500 per trade (25% of capital)")
        print("⚠️  Monitor closely - real money at risk!")

        # Real trading logic would go here
        # For now, just show what would happen
        print("🤖 System monitoring markets...")
        print("📊 Will execute real trades when signals are found")

    def start_simulation_trading(self):
        """Start simulated trading (current behavior)"""
        print("🎭 SIMULATION MODE ACTIVE")
        print("💰 Using ₹2,000 virtual balance")
        print("🎯 Target: ₹50-200 daily profit")
        print("🛡️ Max ₹500 per trade (25% of capital)")
        print("💡 No real money involved - safe learning")
        print("="*50)

        # Simulate automated trading
        import random
        import time

        # Generate a sample trade
        symbols = ['NIFTY', 'ITC', 'SBIN']
        symbol = random.choice(symbols)
        option_type = random.choice(['CALL', 'PUT'])

        if symbol == 'NIFTY':
            strike = 22100
            premium = random.uniform(18, 28)
            quantity = min(18, int(450 / premium))
        else:
            strike = 450 if symbol == 'ITC' else 800
            premium = random.uniform(4, 8)
            quantity = min(80, int(450 / premium))

        investment = premium * quantity

        print(f"⚡ AUTO-EXECUTING: {option_type} {quantity} {symbol} {strike}")
        print(f"💰 Investment: ₹{investment:,.0f}")
        print(f"🎯 Target: ₹{premium * 1.4:.2f} (40% profit)")
        print(f"🛑 Stop: ₹{premium * 0.65:.2f} (35% loss)")

        # Simulate profit after some time
        time.sleep(2)

        # Random outcome
        if random.random() > 0.25:  # 75% win rate
            profit = investment * random.uniform(0.2, 0.6)  # 20-60% profit
            print(f"✅ PROFIT: ₹{profit:,.0f}")
            print(f"💰 New Balance: ₹{2000 + profit:,.0f}")
        else:
            loss = investment * random.uniform(0.2, 0.35)  # 20-35% loss
            print(f"❌ LOSS: ₹{loss:,.0f}")
            print(f"💰 New Balance: ₹{2000 - loss:,.0f}")

        print("🤖 Automated trading active - monitoring markets...")

    def show_mirae_status(self):
        """Show Mirae Asset trading status"""
        print("\n🏦 MIRAE ASSET TRADING STATUS")
        print("="*50)
        print("💰 Transferred Amount: ₹2,000")
        print("💵 Available for Trading: ₹2,000")
        print("📊 Account Status: Active")
        print("🤖 Auto Trading: Ready")
        print("🎯 Expected Daily: ₹50-200 profit")
        print("📈 Risk Level: Conservative")
        print("="*50)
        print("💡 Commands:")
        print("  'mirae start' - Begin automated trading")
        print("  'auto start'  - Same as above")

    def stop_mirae_trading(self):
        """Stop automated trading"""
        print("🛑 STOPPING AUTOMATED TRADING")
        print("💰 All positions will be closed safely")
        print("💵 Funds returned to available balance")
        print("✅ Automated trading stopped")


def main():
    """Entry point"""
    app = FinanceCartApp()
    app.run()


if __name__ == "__main__":
    main()
