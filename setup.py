#!/usr/bin/env python3
"""
Simple Setup for Zerodha Trading
================================
Install everything needed for real money trading.
"""

import subprocess
import sys

def install_package(package):
    """Install a package"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package}")
        return True
    except:
        print(f"❌ {package}")
        return False

def main():
    print("🚀 ZERODHA TRADING SETUP")
    print("="*30)
    
    packages = ["kiteconnect", "pandas", "numpy", "requests"]
    
    print("📦 Installing packages...")
    for pkg in packages:
        install_package(pkg)
    
    print("\n✅ SETUP COMPLETE!")
    print("🎯 Next: python3 zerodha_trader.py")

if __name__ == "__main__":
    main()
