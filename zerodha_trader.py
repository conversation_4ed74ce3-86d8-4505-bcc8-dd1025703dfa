#!/usr/bin/env python3
"""
Zerodha Real Money Trader
========================
Simple automated trading for ₹2,000 capital.

⚠️ WARNING: This trades with REAL MONEY!

Usage: python3 zerodha_trader.py
"""

import time
import datetime
import logging
import getpass
from typing import Dict, List, Optional

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from kiteconnect import KiteConnect
    KITE_AVAILABLE = True
except ImportError:
    KITE_AVAILABLE = False
    print("❌ Install kiteconnect: pip install kiteconnect")

class ZerodhaTrader:
    """Simple Zerodha automated trader"""
    
    def __init__(self, capital=2000):
        self.capital = capital
        self.available = capital
        self.positions = []
        self.total_pnl = 0
        self.trades = 0
        self.wins = 0
        
        # Trading settings
        self.max_per_trade = capital * 0.25  # ₹500 max
        self.stop_loss = 0.30  # 30%
        self.target = 0.40     # 40%
        self.max_positions = 2
        
        self.kite = None
        self.connected = False
        
        logger.info(f"💰 Capital: ₹{capital:,} | Max per trade: ₹{self.max_per_trade:,}")
    
    def connect(self):
        """Connect to Zerodha"""
        if not KITE_AVAILABLE:
            print("❌ KiteConnect not installed!")
            return False
        
        print("🔗 ZERODHA CONNECTION")
        print("="*30)
        print("⚠️ This connects to your REAL account")
        
        api_key = input("API Key: ").strip()
        access_token = getpass.getpass("Access Token: ")
        
        if not api_key or not access_token:
            print("❌ Both API key and access token required")
            return False
        
        try:
            self.kite = KiteConnect(api_key=api_key)
            self.kite.set_access_token(access_token)
            
            profile = self.kite.profile()
            if profile and 'user_id' in profile:
                self.connected = True
                print(f"✅ Connected: {profile.get('user_name', 'N/A')}")
                
                margins = self.kite.margins()
                cash = margins['equity']['available']['cash']
                print(f"💰 Available: ₹{cash:,}")
                
                if cash < self.capital:
                    print(f"⚠️ Low balance: ₹{cash:,} < ₹{self.capital:,}")
                
                return True
            else:
                print("❌ Invalid response")
                return False
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def get_nifty_price(self):
        """Get NIFTY current price"""
        try:
            ltp = self.kite.ltp(["NSE:NIFTY 50"])
            return ltp["NSE:NIFTY 50"]["last_price"]
        except:
            return None
    
    def get_options_data(self):
        """Get NIFTY options data"""
        try:
            nifty_price = self.get_nifty_price()
            if not nifty_price:
                return []
            
            # Get ATM strike
            atm = round(nifty_price / 50) * 50
            
            # Get instruments
            instruments = self.kite.instruments("NFO")
            nifty_options = [
                inst for inst in instruments 
                if inst['name'] == 'NIFTY' 
                and inst['instrument_type'] in ['CE', 'PE']
                and abs(inst['strike'] - atm) <= 150
            ]
            
            if not nifty_options:
                return []
            
            # Get current expiry
            current_expiry = min(opt['expiry'] for opt in nifty_options)
            current_options = [opt for opt in nifty_options if opt['expiry'] == current_expiry]
            
            # Get LTP
            symbols = [f"NFO:{opt['tradingsymbol']}" for opt in current_options[:10]]
            if symbols:
                ltp_data = self.kite.ltp(symbols)
                
                options = []
                for opt in current_options[:10]:
                    symbol_key = f"NFO:{opt['tradingsymbol']}"
                    if symbol_key in ltp_data:
                        options.append({
                            'symbol': opt['tradingsymbol'],
                            'strike': opt['strike'],
                            'type': opt['instrument_type'],
                            'price': ltp_data[symbol_key]['last_price'],
                            'spot': nifty_price
                        })
                
                return options
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting options: {e}")
            return []
    
    def calculate_rsi(self):
        """Calculate simple RSI"""
        try:
            # Get NIFTY token
            instruments = self.kite.instruments("NSE")
            nifty_token = None
            for inst in instruments:
                if inst['tradingsymbol'] == 'NIFTY 50':
                    nifty_token = inst['instrument_token']
                    break
            
            if not nifty_token:
                return 50
            
            # Get historical data
            from_date = datetime.date.today() - datetime.timedelta(days=20)
            to_date = datetime.date.today()
            
            data = self.kite.historical_data(nifty_token, from_date, to_date, "15minute")
            
            if len(data) < 15:
                return 50
            
            prices = [candle['close'] for candle in data]
            
            gains = []
            losses = []
            
            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            if len(gains) < 14:
                return 50
            
            avg_gain = sum(gains[-14:]) / 14
            avg_loss = sum(losses[-14:]) / 14
            
            if avg_loss == 0:
                return 100
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return round(rsi, 1)
            
        except Exception as e:
            logger.error(f"RSI calculation error: {e}")
            return 50
    
    def generate_signals(self):
        """Generate trading signals"""
        options = self.get_options_data()
        if not options:
            return []
        
        rsi = self.calculate_rsi()
        logger.info(f"📊 NIFTY RSI: {rsi}")
        
        signals = []
        
        for option in options:
            confidence = 0
            reasons = []
            
            # RSI signals
            if rsi < 35 and option['type'] == 'CE':
                confidence += 0.4
                reasons.append(f"Oversold RSI ({rsi})")
            elif rsi > 65 and option['type'] == 'PE':
                confidence += 0.4
                reasons.append(f"Overbought RSI ({rsi})")
            
            # Price filter
            premium = option['price']
            if 10 <= premium <= 30:  # Good premium range
                confidence += 0.2
                reasons.append("Good premium")
            
            # ATM/OTM filter
            spot = option['spot']
            strike = option['strike']
            
            if option['type'] == 'CE' and strike <= spot + 100:
                confidence += 0.2
                reasons.append("Near ATM Call")
            elif option['type'] == 'PE' and strike >= spot - 100:
                confidence += 0.2
                reasons.append("Near ATM Put")
            
            # Time filter
            current_time = datetime.datetime.now().time()
            if datetime.time(9, 45) <= current_time <= datetime.time(15, 0):
                confidence += 0.1
                reasons.append("Good hours")
            
            # Generate signal if good enough
            if confidence >= 0.7 and len(reasons) >= 2:
                quantity = min(50, int(self.max_per_trade / premium))
                investment = premium * quantity
                
                if investment <= self.max_per_trade and investment <= self.available:
                    signals.append({
                        'symbol': option['symbol'],
                        'type': option['type'],
                        'strike': option['strike'],
                        'price': premium,
                        'quantity': quantity,
                        'investment': investment,
                        'confidence': confidence,
                        'reasons': reasons
                    })
        
        # Sort by confidence
        signals.sort(key=lambda x: x['confidence'], reverse=True)
        return signals[:2]  # Top 2
    
    def place_order(self, signal):
        """Place real order"""
        try:
            print(f"\n⚡ PLACING ORDER:")
            print(f"   {signal['symbol']} {signal['type']}")
            print(f"   Quantity: {signal['quantity']}")
            print(f"   Price: ₹{signal['price']}")
            print(f"   Investment: ₹{signal['investment']:,}")
            print(f"   Confidence: {signal['confidence']:.1%}")
            
            confirm = input("\nConfirm order? (yes/no): ")
            if confirm.lower() != 'yes':
                print("❌ Order cancelled")
                return False
            
            order_id = self.kite.place_order(
                variety="regular",
                exchange="NFO",
                tradingsymbol=signal['symbol'],
                transaction_type="BUY",
                quantity=signal['quantity'],
                product="MIS",
                order_type="MARKET"
            )
            
            if order_id:
                print(f"✅ ORDER PLACED: {order_id}")
                
                position = {
                    'symbol': signal['symbol'],
                    'type': signal['type'],
                    'strike': signal['strike'],
                    'quantity': signal['quantity'],
                    'entry_price': signal['price'],
                    'current_price': signal['price'],
                    'investment': signal['investment'],
                    'stop_loss': signal['price'] * (1 - self.stop_loss),
                    'target': signal['price'] * (1 + self.target),
                    'entry_time': datetime.datetime.now(),
                    'pnl': 0,
                    'order_id': order_id
                }
                
                self.positions.append(position)
                self.available -= signal['investment']
                self.trades += 1
                
                return True
            else:
                print("❌ Order failed")
                return False
                
        except Exception as e:
            print(f"❌ Order error: {e}")
            return False
    
    def monitor_positions(self):
        """Monitor positions"""
        if not self.positions:
            return
        
        logger.info(f"📊 Monitoring {len(self.positions)} positions")
        
        for pos in self.positions[:]:
            try:
                symbol_key = f"NFO:{pos['symbol']}"
                ltp_data = self.kite.ltp([symbol_key])
                
                if symbol_key in ltp_data:
                    current_price = ltp_data[symbol_key]['last_price']
                    pos['current_price'] = current_price
                    pos['pnl'] = (current_price - pos['entry_price']) * pos['quantity']
                    
                    logger.info(f"📈 {pos['symbol']}: ₹{pos['entry_price']:.2f} → ₹{current_price:.2f} | P&L: ₹{pos['pnl']:,.0f}")
                    
                    # Check exit conditions
                    if current_price >= pos['target']:
                        self.close_position(pos, current_price, "TARGET")
                    elif current_price <= pos['stop_loss']:
                        self.close_position(pos, current_price, "STOP_LOSS")
                    elif self.should_exit_time(pos):
                        self.close_position(pos, current_price, "TIME_EXIT")
                
            except Exception as e:
                logger.error(f"Monitor error for {pos['symbol']}: {e}")
    
    def close_position(self, pos, exit_price, reason):
        """Close position"""
        try:
            logger.info(f"🔄 CLOSING: {pos['symbol']} - {reason}")
            
            order_id = self.kite.place_order(
                variety="regular",
                exchange="NFO",
                tradingsymbol=pos['symbol'],
                transaction_type="SELL",
                quantity=pos['quantity'],
                product="MIS",
                order_type="MARKET"
            )
            
            if order_id:
                pnl = (exit_price - pos['entry_price']) * pos['quantity']
                self.total_pnl += pnl
                self.available += exit_price * pos['quantity']
                
                if pnl > 0:
                    self.wins += 1
                    print(f"✅ PROFIT: ₹{pnl:,.0f}")
                else:
                    print(f"❌ LOSS: ₹{pnl:,.0f}")
                
                self.positions.remove(pos)
                print(f"💰 Available: ₹{self.available:,.0f}")
                
        except Exception as e:
            logger.error(f"Close error: {e}")
    
    def should_exit_time(self, pos):
        """Check time-based exit"""
        holding_time = datetime.datetime.now() - pos['entry_time']
        current_time = datetime.datetime.now().time()
        
        # Exit before market close
        if current_time >= datetime.time(15, 0):
            return True
        
        # Exit after 3 hours
        if holding_time.total_seconds() > 3 * 3600:
            return True
        
        return False
    
    def show_status(self):
        """Show trading status"""
        total_capital = self.available + sum(p['investment'] for p in self.positions)
        win_rate = (self.wins / self.trades * 100) if self.trades > 0 else 0
        roi = (self.total_pnl / self.capital * 100)
        
        print(f"\n💰 TRADING STATUS")
        print("="*40)
        print(f"💵 Capital: ₹{total_capital:,.0f}")
        print(f"📊 Available: ₹{self.available:,.0f}")
        print(f"🎯 Positions: {len(self.positions)}")
        print(f"📈 Trades: {self.trades}")
        print(f"✅ Wins: {self.wins}")
        print(f"🎯 Win Rate: {win_rate:.1f}%")
        print(f"💹 P&L: ₹{self.total_pnl:,.0f}")
        print(f"📊 ROI: {roi:.2f}%")
        print("="*40)
    
    def start_trading(self):
        """Start automated trading"""
        if not self.connected:
            print("❌ Not connected")
            return
        
        print("\n🚀 STARTING AUTOMATED TRADING")
        print("💰 This trades with REAL MONEY!")
        print("⚠️ Press Ctrl+C to stop")
        
        try:
            cycle = 0
            while cycle < 20:  # Safety limit
                cycle += 1
                print(f"\n🔄 Cycle {cycle}")
                
                # Check market hours
                current_time = datetime.datetime.now().time()
                if not (datetime.time(9, 15) <= current_time <= datetime.time(15, 30)):
                    print("⏰ Market closed")
                    break
                
                # Monitor positions
                self.monitor_positions()
                
                # Generate signals if capacity available
                if len(self.positions) < self.max_positions:
                    signals = self.generate_signals()
                    
                    if signals:
                        best_signal = signals[0]
                        if best_signal['investment'] <= self.available:
                            self.place_order(best_signal)
                
                # Show status
                self.show_status()
                
                # Wait 5 minutes
                if cycle < 20:
                    print("⏰ Next cycle in 5 minutes...")
                    time.sleep(300)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        
        # Close all positions
        for pos in self.positions[:]:
            try:
                symbol_key = f"NFO:{pos['symbol']}"
                ltp_data = self.kite.ltp([symbol_key])
                if symbol_key in ltp_data:
                    current_price = ltp_data[symbol_key]['last_price']
                    self.close_position(pos, current_price, "SYSTEM_STOP")
            except:
                pass
        
        self.show_status()
        print("👋 Trading completed")


def main():
    print("💰 ZERODHA REAL MONEY TRADER")
    print("="*40)
    print("⚠️ WARNING: Uses REAL MONEY!")
    print("🎯 Optimized for ₹2,000 capital")
    print("="*40)
    
    trader = ZerodhaTrader(2000)
    
    if not trader.connect():
        print("❌ Connection failed")
        return
    
    print(f"\n⚠️ FINAL CONFIRMATION")
    print(f"💰 Capital: ₹{trader.capital:,}")
    print(f"🎯 Max per trade: ₹{trader.max_per_trade:,}")
    
    confirm = input("\nStart REAL trading? (type 'START'): ")
    
    if confirm == 'START':
        trader.start_trading()
    else:
        print("❌ Trading cancelled")


if __name__ == "__main__":
    main()
